import json

from app.models.prompt_request import PromptRequest
from app.models.therapist_response import TherapistResponse
from app.services import client

class TherapistService:
    def get_therapist_response(self, p: PromptRequest):
        response = client.responses.create(
            model="gpt-4o-mini",
            input=p.prompt,
            previous_response_id=p.previous_message_id,
        )

        resp = TherapistResponse(
            message_id=response.id,
            message=response.output_text,
        )

        return resp
