from fastapi import APIRouter, Request

from app.models.prompt_request import PromptRequest
from app.services.therapist import TherapistService
from app.middleware import get_current_user

router = APIRouter()

@router.post("/therapist")
def get_therapist_response(p: PromptRequest, request: Request):
    # The middleware already validates authentication, but we can get user info if needed
    user = get_current_user(request)

    service = TherapistService()
    resp = service.get_therapist_response(p)
    return resp
