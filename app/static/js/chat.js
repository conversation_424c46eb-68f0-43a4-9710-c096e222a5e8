'use strict';
const { useState, useRef, useEffect } = React;
const { createRoot } = ReactDOM;

const ChatApp = () => {
    const [messages, setMessages] = useState([]);
    const [inputText, setInputText] = useState('');
    const [previousResponseId, setPreviousResponseId] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isGeneratingImages, setIsGeneratingImages] = useState(false); // State for image generation loading
    const messagesEndRef = useRef(null);
    const chatWindowRef = useRef(null);
    const prevLen = useRef(0);

    const scrollToBottom = () => {
        if (chatWindowRef.current) {
            chatWindowRef.current.scrollTop = chatWindowRef.current.scrollHeight;
        }
    };

    useEffect(() => {
        if (messages.length > prevLen.current) {
            scrollToBottom();
        }
        prevLen.current = messages.length;
    }, [messages]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!inputText.trim()) return;

        setMessages(prev => [...prev, { type: 'user', content: inputText }]);
        setIsLoading(true);

        try {
            const response = await fetch('/api/v1/therapist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: inputText,
                    ...(previousResponseId && { previous_message_id: previousResponseId }), // Include previous_message_id if available
                }),
            });

            const data = await response.json();

            if (data.message_id) { // Store the received message ID
                setPreviousResponseId(data.message_id);
            }

            setMessages(prev => [...prev, { type: 'assistant', content: data.message }]);
        } catch (error) {
            console.error('Error:', error);
            setPreviousResponseId(null); // Reset on error
            setMessages(prev => [...prev, {
                type: 'error',
                content: 'Sorry, there was an error sending your message.'
            }]);
        } finally {
            setIsLoading(false);
            setInputText('');
        }
    };

    const handleGenerateImages = async () => {
        if (!previousResponseId || isGeneratingImages) return;

        setIsGeneratingImages(true);
        console.log("Sending to /image:", previousResponseId); // Debug log

        try {
            const response = await fetch('/api/v1/image', { // Changed endpoint
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    response_id: previousResponseId
                }),
            });

            const imageData = await response.json();
            console.log('Image generation response:', imageData); // Log response for now
            // TODO: Handle the image response (e.g., display the image)

        } catch (error) {
            console.error('Image generation error:', error);
            // Optionally show an error message to the user
        } finally {
            setIsGeneratingImages(false);
        }
    };


    return React.createElement('div', {
        className: "flex flex-col h-full min-h-0 max-w-3xl mx-auto p-4 bg-gray-50"
    }, [
        // Chat Window
        React.createElement('div', {
            key: 'chat-window',
            ref: chatWindowRef,
            className: "flex-1 min-h-0 overflow-y-auto overscroll-contain mb-4 bg-white rounded-lg shadow p-4"
        },
            React.createElement('div', {
                className: "space-y-4"
            }, [
                messages.map((message, index) =>
                    React.createElement('div', {
                        key: index,
                        className: `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`
                    },
                        // Outer bubble div - keep padding, rounding, max-width
                        React.createElement('div', {
                            className: `max-w-[80%] px-4 py-2 rounded-lg ${message.type === 'user'
                                ? 'bg-blue-500 text-white rounded-br-none' // User style
                                : message.type === 'error'
                                    ? 'bg-red-100 text-red-700' // Error style
                                    : 'bg-gray-100 rounded-bl-none' // Assistant bubble style (no text color)
                                }`
                        }, [ // Render children for all types now
                            message.type === 'assistant'
                                // Inner div for assistant markdown content
                                ? React.createElement('div', {
                                    className: 'markdown-body', // Apply markdown styles here
                                    // Reset padding/margin and make background transparent
                                    style: { padding: 0, margin: 0, maxWidth: 'none', backgroundColor: 'transparent' },
                                    dangerouslySetInnerHTML: { __html: marked.parse(message.content || '') }
                                })
                                // Render text directly for user/error messages
                                : message.content
                        ])
                    )
                ),
                isLoading &&
                React.createElement('div', {
                    key: 'loading',
                    className: "flex justify-start"
                },
                    React.createElement('div', {
                        className: "bg-gray-100 text-gray-800 rounded-lg rounded-bl-none px-4 py-2"
                    },
                        React.createElement('div', {
                            className: "flex space-x-2"
                        }, [
                            React.createElement('div', {
                                key: 'dot1',
                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                style: { animationDelay: '0ms' }
                            }),
                            React.createElement('div', {
                                key: 'dot2',
                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                style: { animationDelay: '150ms' }
                            }),
                            React.createElement('div', {
                                key: 'dot3',
                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                style: { animationDelay: '300ms' }
                            })
                        ])
                    )
                ),
                React.createElement('div', {
                    key: 'scroll-anchor',
                    ref: messagesEndRef
                })
            ])
        ),

        // Input Form
        React.createElement('form', {
            key: 'input-form',
            onSubmit: handleSubmit,
            className: "flex space-x-2"
        }, [
            React.createElement('input', {
                key: 'text-input',
                type: "text",
                value: inputText,
                onChange: (e) => setInputText(e.target.value),
                placeholder: "Type your message...",
                className: "flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            }),
            React.createElement('button', {
                key: 'submit-button',
                type: "submit",
                disabled: isLoading || !inputText.trim(),
                className: "px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center"
            },
                // Create an SVG icon instead of using Lucide component
                React.createElement('svg', {
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20",
                    height: "20",
                    viewBox: "0 0 24 24",
                    fill: "none",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, [
                    React.createElement('path', {
                        key: 'path1',
                        d: "M22 2L11 13"
                    }),
                    React.createElement('path', {
                        key: 'path2',
                        d: "M22 2L15 22L11 13L2 9L22 2"
                    })
                ])
            )
        ])
    ]);
};

// Mount the React application
const root = createRoot(document.getElementById('root'));
root.render(React.createElement(ChatApp));
