import os

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    OPENAI_API_KEY: str = os.environ.get('OPENAI_API_KEY', '')
    PROJECT_ID: str = "proj_cLnwB8bC3ogYPqxOsXwePvuE"

    # Session/Auth settings
    SECRET_KEY: str = os.environ.get('SECRET_KEY', '')

    # Google OAuth (OIDC) credentials
    GOOGLE_CLIENT_ID: str = os.environ.get('GOOGLE_CLIENT_ID', '')
    GOOGLE_CLIENT_SECRET: str = os.environ.get('GOOGLE_CLIENT_SECRET', '')


settings = Settings()